<?php
namespace App\Http\Controllers;

use App\Models\Laboratoire;
use Illuminate\Support\Str;

use Illuminate\View\View;

class LaboController extends Controller
{
    /*public function index()
    {
        $labos = laboratoire::all();
        return view('acceuil',[
          'labos'=>$labos,  
        ]);
    }*/

    public function index()
    {
        $labos = Laboratoire::all()->map(function ($labo) {
            return [
                'id' => $labo->id,
                'nom' => $labo->nom,
                'description' => Str::limit($labo->description, 100, '...'), // Tronquer à 100 caractères
                'thumbnail' => $labo->thumbnail, // Assurez-vous que cette clé existe
            ];
        });
    
        return view('acceuil', [
            'labos' => $labos,
        ]);
    
}

public function show(Laboratoire $labo): View
{
    $labo->load('chef', 'travail_pratiques'); 
    
    $labo->chef; $labo->travail_pratiques;
    // Charger le chef et les TP
    return view('labo', [
        'labo' => $labo,
    ]);
}
}
