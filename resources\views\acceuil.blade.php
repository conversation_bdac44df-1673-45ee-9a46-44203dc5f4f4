@extends('navfoter')

@section('content')
<div class="hero-section">
    <h2>PLATEFORME DE PRÉSENTATION DES <p1>LABORATOIRES PÉDAGOGIQUES</p1> DE L'INSTI</h2>
    <p>Explorez l'innovation au cœur de l'apprentissage</p>
</div>
</header>
<div class="container">
 <h1>Les Laboratoires</h1>   


<div class="labs-grid">
    <!-- Affichage des Labo -->
    @foreach ($labos as $labo)
        
    <a class="nav-link" href="{{ route('laboratoires.show', ['labo' => $labo['nom']]) }}">
        <div class="lab-card">
        <img src="{{ asset('storage/' . $labo['thumbnail']) }}" alt="Laboratoire de {{ $labo['nom'] }}" 
        class="lab-image">
        <div class="lab-content">
            <div class="lab-description">{{$labo['description']}} </div>
            
        </div>
        
    </div>
    <div class="lab-type">{{$labo['nom']}} </div></a>
    
@endforeach
</div>
</div>


<div class="carousel-container">
<div class="carousel">
    <div class="carousel-slide">
        <img src="{{asset('images/lab6.jpg')}}" alt="Image 1">
    </div>
    <div class="carousel-slide">
        <img src="{{asset('images/background.jpg')}}" alt="Image 2">
    </div>
    <div class="carousel-slide">
        <img src="{{asset('images/mahine3.jpg')}}" alt="Image 3">
    </div>
    
   

    <!-- Ajoutez autant de slides que nécessaire -->
</div>


</div>
<div class="carousel-nav">
    <button class="carousel-button prev">&lt;</button>
<span class="carousel-dot"></span>
<span class="carousel-dot"></span>
<span class="carousel-dot"></span>
<button class="carousel-button next">&gt;</button>
</div>

<script>
document.addEventListener('DOMContentLoaded', function () {
const carousel = document.querySelector('.carousel');
const slides = document.querySelectorAll('.carousel-slide');
const dots = document.querySelectorAll('.carousel-dot');
const prevButton = document.querySelector('.prev');
const nextButton = document.querySelector('.next');

let currentSlide = 0;
const slideCount = slides.length;

function updateCarousel() {
const offset = currentSlide * (100 - 0 / slideCount); // Décalage avec espace
carousel.style.transform = `translateX(-${offset}%)`;

dots.forEach(dot => dot.classList.remove('active'));
dots[currentSlide].classList.add('active');
}

function nextSlide() {
currentSlide = (currentSlide + 1) % slideCount;
updateCarousel();
}

function prevSlide() {
currentSlide = (currentSlide - 1 + slideCount) % slideCount;
updateCarousel();
}

let slideInterval = setInterval(nextSlide, 3000);

nextButton.addEventListener('click', () => {
clearInterval(slideInterval);
nextSlide();
slideInterval = setInterval(nextSlide, 3000);
});

prevButton.addEventListener('click', () => {
clearInterval(slideInterval);
prevSlide();
slideInterval = setInterval(nextSlide, 3000);
});

dots.forEach((dot, index) => {
dot.addEventListener('click', () => {
    clearInterval(slideInterval);
    currentSlide = index;
    updateCarousel();
    slideInterval = setInterval(nextSlide, 3000);
});
});

carousel.addEventListener('mouseenter', () => clearInterval(slideInterval));
carousel.addEventListener('mouseleave', () => slideInterval = setInterval(nextSlide, 3000));

updateCarousel();
});
</script>



@endsection