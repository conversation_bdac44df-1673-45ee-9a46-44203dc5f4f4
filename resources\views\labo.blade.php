<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{{ $labo->nom }} - Laboratoire Scientifique</title>
  @vite(['resources/css/labostyles.css'])
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
  <header>
    <nav>
      <div class="logo">
        <h1>Lab<span>Science</span></h1>
      </div>
      <ul>
        <li><a href="#accueil">Accueil</a></li>
        <li><a href="#about">À propos</a></li>
        <li><a href="#equipements">Équipements</a></li>
        <li><a href="#tutoriels">Tutoriels</a></li>
        <li><a href="#equipe">Équipe</a></li>
      </ul>
    </nav>
  </header>

  <main>
    <section id="hero">
      <div class="hero-image">
        <img src="{{ asset('images/background.jpg') }}" alt="Laboratoire">
        <div class="hero-text">
          <h2>BIENVENUE SUR LA PAGE DE LABORATOIRE DE {{ strtoupper($labo->nom) }}</h2>
          <p>Explorez l'innovation au cœur de l'apprentissage</p>
        </div>
      </div>
    </section>
  
    <div class="container">
      <h1>{{ $labo->nom }}</h1>
      <p>Description : {{ $labo->description }}</p>

      <h2>Chef de laboratoire</h2>
      @if ($labo->chef)
          <p>Nom : {{ $labo->chef->nom }}</p>
          <p>Email : {{ $labo->chef->email }}</p>
      @else
          <p>Aucun chef assigné.</p>
      @endif
    </div>
  
    <section id="about" class="container">
      <h2>{{ $labo->nom }}</h2>
      <p>{{ $labo->description }}</p>
      
      <div class="objectifs">
        <h3>Nos Objectifs</h3>
        <ul>
          <li>Développer des solutions innovantes pour l'industrie</li>
          <li>Former la prochaine génération de chercheurs</li>
          <li>Contribuer à l'avancement des connaissances scientifiques</li>
          <li>Collaborer avec des partenaires internationaux</li>
        </ul>
      </div>
    </section>

    <section id="equipements" class="container">
      <h2>LES TRAVAUX PRATIQUES</h2>
      <div class="equipment-grid">
        @forelse($labo->travail_pratiques as $tp)
        <div class="equipment-item">
          <div class="description">
            <h4>{{ $tp->nom }}</h4>
            <p>{{ $tp->description }}</p>
          </div>
          <div class="equipment-gallery" data-images='@json($tp->images->pluck("chemin")->toArray())'>
            @if($tp->images->isNotEmpty())
              <img src="{{ asset('storage/' . $tp->images->first()->chemin) }}" alt="{{ $tp->nom }}">
            @else
              <img src="https://images.unsplash.com/photo-1582719508461-905c673771fd" alt="{{ $tp->nom }}">
            @endif
          </div>
        </div>
        @empty
        <div class="equipment-item">
          <div class="description">
            <h4>Aucun travail pratique disponible</h4>
            <p>Les travaux pratiques seront bientôt disponibles.</p>
          </div>
        </div>
        @endforelse
      </div>
    </section>

    <section id="equipe" class="container">
      <h2>Chef laboratoire</h2>
      <div class="team-lead">
        @if($labo->chef && $labo->chef->photo)
          <img src="{{ asset('storage/' . $labo->chef->photo) }}" alt="Chef du laboratoire">
        @else
          <img src="https://images.unsplash.com/photo-1559839734-2b71ea197ec2" alt="Chef du laboratoire">
        @endif
        <div class="team-info">
          @if($labo->chef)
            <h3>{{ $labo->chef->nom }}</h3>
            <h4>Chef du Laboratoire {{ $labo->nom }}</h4>
            <p>{{ $labo->chef->bio ?? 'Responsable du laboratoire avec une expertise reconnue dans le domaine.' }}</p>
          @else
            <h3>Chef à désigner</h3>
            <h4>Poste vacant</h4>
            <p>Le laboratoire recherche actuellement un chef qualifié.</p>
          @endif
        </div>
      </div>
    </section>
  </main>

  <div class="modal" id="imageModal">
    <div class="modal-content">
      <button class="close">&times;</button>
      <div class="slideshow">
        <!-- Slides will be inserted here by JavaScript -->
      </div>
      <button class="prev">&lt;</button>
      <button class="next">&gt;</button>
    </div>
  </div>

  <footer>
    <div class="footer-content container">
      <div class="footer-section">
        <h3>Contact</h3>
        <p><i class="fas fa-envelope"></i> <EMAIL></p>
        <p><i class="fas fa-phone"></i> +33 1 23 45 67 89</p>
        <p><i class="fas fa-map-marker-alt"></i> 123 Rue de la Science, Paris</p>
      </div>
      <div class="footer-section">
        <h3>Suivez-nous</h3>
        <div class="social-links">
          <a href="https://twitter.com"><i class="fab fa-twitter"></i></a>
          <a href="https://linkedin.com"><i class="fab fa-linkedin"></i></a>
          <a href="https://facebook.com"><i class="fab fa-facebook"></i></a>
        </div>
      </div>
    </div>
    <div class="footer-bottom">
      <p>&copy; 2024 LabScience. Tous droits réservés.</p>
    </div>
  </footer>

  <script>
    // Scroll reveal animation
    function reveal() {
      const reveals = document.querySelectorAll("section");
      
      reveals.forEach(reveal => {
        const windowHeight = window.innerHeight;
        const elementTop = reveal.getBoundingClientRect().top;
        const elementVisible = 150;
        
        if (elementTop < windowHeight - elementVisible) {
          reveal.classList.add("active");
        }
      });
    }

    window.addEventListener("scroll", reveal);
    reveal();

    // Image Gallery functionality
    document.querySelectorAll('.equipment-gallery').forEach(gallery => {
      gallery.addEventListener('click', function() {
        const images = JSON.parse(this.dataset.images);
        const modal = document.getElementById('imageModal');
        const slideshow = modal.querySelector('.slideshow');
        
        slideshow.innerHTML = '';
        
        images.forEach((src, index) => {
          const slide = document.createElement('div');
          slide.className = `slide ${index === 0 ? 'active' : ''}`;
          slide.innerHTML = `<img src="/storage/${src}" alt="Equipment image ${index + 1}">`;
          slideshow.appendChild(slide);
        });
        
        modal.style.display = 'block';
      });
    });

    // Modal controls
    const modal = document.getElementById('imageModal');
    const closeBtn = modal.querySelector('.close');
    const prevBtn = modal.querySelector('.prev');
    const nextBtn = modal.querySelector('.next');

    closeBtn.onclick = () => modal.style.display = 'none';

    let currentSlide = 0;

    function showSlide(n) {
      const slides = modal.querySelectorAll('.slide');
      
      slides.forEach(slide => slide.classList.remove('active'));
      
      currentSlide = (n + slides.length) % slides.length;
      slides[currentSlide].classList.add('active');
    }

    prevBtn.onclick = () => showSlide(currentSlide - 1);
    nextBtn.onclick = () => showSlide(currentSlide + 1);

    window.onclick = (e) => {
      if (e.target === modal) modal.style.display = 'none';
    }
  </script>
</body>
</html>