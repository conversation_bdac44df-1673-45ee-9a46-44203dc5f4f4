<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{{ $labo->nom }} - Laboratoire Scientifique</title>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
  <style>
    /* Styles pour la page laboratoire */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: #333;
      background-color: #f8f9fa;
    }

    /* Header et Navigation */
    header {
      background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
      padding: 1rem 0;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      position: sticky;
      top: 0;
      z-index: 1000;
    }

    nav {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 2rem;
    }

    .logo h1 {
      color: white;
      font-size: 2rem;
      font-weight: bold;
    }

    .logo span {
      color: #ffd700;
    }

    nav ul {
      display: flex;
      list-style: none;
      gap: 2rem;
    }

    nav ul li a {
      color: white;
      text-decoration: none;
      font-weight: 500;
      transition: color 0.3s ease;
      padding: 0.5rem 1rem;
      border-radius: 5px;
    }

    nav ul li a:hover {
      color: #ffd700;
      background-color: rgba(255,255,255,0.1);
    }

    /* Hero Section */
    #hero {
      position: relative;
      height: 400px;
      overflow: hidden;
      margin-bottom: 3rem;
    }

    .hero-image {
      position: relative;
      width: 100%;
      height: 100%;
    }

    .hero-image img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .hero-text {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      color: white;
      background: rgba(0,0,0,0.6);
      padding: 2rem;
      border-radius: 10px;
      backdrop-filter: blur(5px);
    }

    .hero-text h2 {
      font-size: 2.5rem;
      margin-bottom: 1rem;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    }

    .hero-text p {
      font-size: 1.2rem;
      opacity: 0.9;
    }

    /* Container */
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 2rem;
      margin-bottom: 3rem;
    }

    .container h1 {
      color: #2a5298;
      font-size: 2.5rem;
      margin-bottom: 1rem;
      text-align: center;
    }

    .container h2 {
      color: #2a5298;
      font-size: 2rem;
      margin: 2rem 0 1rem 0;
      border-bottom: 3px solid #ffd700;
      padding-bottom: 0.5rem;
    }

    .container p {
      font-size: 1.1rem;
      margin-bottom: 1rem;
      color: #666;
    }

    /* Sections */
    section {
      margin-bottom: 4rem;
      opacity: 0;
      transform: translateY(30px);
      transition: all 0.6s ease;
    }

    section.active {
      opacity: 1;
      transform: translateY(0);
    }

    #about {
      background: white;
      padding: 3rem;
      border-radius: 15px;
      box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    }

    .objectifs {
      margin-top: 2rem;
      background: #f8f9fa;
      padding: 2rem;
      border-radius: 10px;
      border-left: 5px solid #2a5298;
    }

    .objectifs h3 {
      color: #2a5298;
      margin-bottom: 1rem;
    }

    .objectifs ul {
      list-style: none;
      padding-left: 0;
    }

    .objectifs li {
      padding: 0.5rem 0;
      position: relative;
      padding-left: 2rem;
    }

    .objectifs li:before {
      content: "✓";
      position: absolute;
      left: 0;
      color: #28a745;
      font-weight: bold;
      font-size: 1.2rem;
    }

    /* Equipment Grid */
    #equipements {
      background: white;
      padding: 3rem;
      border-radius: 15px;
      box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    }

    .equipment-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 2rem;
      margin-top: 2rem;
    }

    .equipment-item {
      background: #f8f9fa;
      border-radius: 15px;
      overflow: hidden;
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
      transition: all 0.3s ease;
    }

    .equipment-item:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 25px rgba(0,0,0,0.2);
    }

    .description {
      padding: 1.5rem;
    }

    .description h4 {
      color: #2a5298;
      font-size: 1.3rem;
      margin-bottom: 1rem;
    }

    .description p {
      color: #666;
      line-height: 1.6;
    }

    .equipment-gallery {
      height: 200px;
      overflow: hidden;
      cursor: pointer;
      position: relative;
    }

    .equipment-gallery img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
    }

    .equipment-gallery:hover img {
      transform: scale(1.1);
    }

    /* Team Section */
    #equipe {
      background: white;
      padding: 3rem;
      border-radius: 15px;
      box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    }

    .team-lead {
      display: flex;
      gap: 2rem;
      align-items: center;
      background: #f8f9fa;
      padding: 2rem;
      border-radius: 15px;
      border-left: 5px solid #2a5298;
    }

    .team-lead img {
      width: 150px;
      height: 150px;
      border-radius: 50%;
      object-fit: cover;
      border: 5px solid #2a5298;
    }

    .team-info h3 {
      color: #2a5298;
      font-size: 1.8rem;
      margin-bottom: 0.5rem;
    }

    .team-info h4 {
      color: #666;
      font-size: 1.2rem;
      margin-bottom: 1rem;
      font-weight: normal;
    }

    .team-info p {
      color: #666;
      line-height: 1.6;
    }

    /* Modal */
    .modal {
      display: none;
      position: fixed;
      z-index: 2000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0,0,0,0.9);
    }

    .modal-content {
      position: relative;
      margin: 5% auto;
      width: 90%;
      max-width: 800px;
      background: white;
      border-radius: 15px;
      overflow: hidden;
    }

    .slideshow {
      position: relative;
      height: 500px;
    }

    .slide {
      display: none;
      width: 100%;
      height: 100%;
    }

    .slide.active {
      display: block;
    }

    .slide img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .close, .prev, .next {
      position: absolute;
      background: rgba(0,0,0,0.7);
      color: white;
      border: none;
      padding: 15px;
      cursor: pointer;
      font-size: 1.5rem;
      transition: background 0.3s ease;
    }

    .close {
      top: 15px;
      right: 15px;
      border-radius: 50%;
      width: 50px;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .prev, .next {
      top: 50%;
      transform: translateY(-50%);
      border-radius: 5px;
    }

    .prev {
      left: 15px;
    }

    .next {
      right: 15px;
    }

    .close:hover, .prev:hover, .next:hover {
      background: rgba(0,0,0,0.9);
    }

    /* Footer */
    footer {
      background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
      color: white;
      padding: 3rem 0 1rem 0;
      margin-top: 4rem;
    }

    .footer-content {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 2rem;
      margin-bottom: 2rem;
    }

    .footer-section h3 {
      color: #ffd700;
      margin-bottom: 1rem;
    }

    .footer-section p, .footer-section a {
      color: rgba(255,255,255,0.8);
      text-decoration: none;
      margin-bottom: 0.5rem;
      display: block;
    }

    .footer-section a:hover {
      color: #ffd700;
    }

    .social-links {
      display: flex;
      gap: 1rem;
      margin-top: 1rem;
    }

    .social-links a {
      display: inline-block;
      width: 40px;
      height: 40px;
      background: rgba(255,255,255,0.1);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: background 0.3s ease;
    }

    .social-links a:hover {
      background: #ffd700;
      color: #2a5298;
    }

    .footer-bottom {
      text-align: center;
      padding-top: 2rem;
      border-top: 1px solid rgba(255,255,255,0.2);
      color: rgba(255,255,255,0.6);
    }

    /* Responsive */
    @media (max-width: 768px) {
      nav {
        flex-direction: column;
        gap: 1rem;
      }

      nav ul {
        flex-wrap: wrap;
        justify-content: center;
        gap: 1rem;
      }

      .hero-text h2 {
        font-size: 1.8rem;
      }

      .container {
        padding: 0 1rem;
      }

      .equipment-grid {
        grid-template-columns: 1fr;
      }

      .team-lead {
        flex-direction: column;
        text-align: center;
      }

      .modal-content {
        width: 95%;
        margin: 10% auto;
      }

      .slideshow {
        height: 300px;
      }
    }
  </style>
</head>
<body>
  <header>
    <nav>
      <div class="logo">
        <h1>Lab<span>Science</span></h1>
      </div>
      <ul>
        <li><a href="#accueil">Accueil</a></li>
        <li><a href="#about">À propos</a></li>
        <li><a href="#equipements">Équipements</a></li>
        <li><a href="#tutoriels">Tutoriels</a></li>
        <li><a href="#equipe">Équipe</a></li>
      </ul>
    </nav>
  </header>

  <main>
    <section id="hero">
      <div class="hero-image">
        <img src="{{ asset('images/background.jpg') }}" alt="Laboratoire">
        <div class="hero-text">
          <h2>BIENVENUE SUR LA PAGE DE LABORATOIRE DE {{ strtoupper($labo->nom) }}</h2>
          <p>Explorez l'innovation au cœur de l'apprentissage</p>
        </div>
      </div>
    </section>
  
    <div class="container">
      <h1>{{ $labo->nom }}</h1>
      <p>Description : {{ $labo->description }}</p>

      <h2>Chef de laboratoire</h2>
      @if ($labo->chef)
          <p>Nom : {{ $labo->chef->nom }}</p>
          <p>Email : {{ $labo->chef->email }}</p>
      @else
          <p>Aucun chef assigné.</p>
      @endif
    </div>
  
    <section id="about" class="container">
      <h2>{{ $labo->nom }}</h2>
      <p>{{ $labo->description }}</p>
      
      <div class="objectifs">
        <h3>Nos Objectifs</h3>
        <ul>
          <li>Développer des solutions innovantes pour l'industrie</li>
          <li>Former la prochaine génération de chercheurs</li>
          <li>Contribuer à l'avancement des connaissances scientifiques</li>
          <li>Collaborer avec des partenaires internationaux</li>
        </ul>
      </div>
    </section>

    <section id="equipements" class="container">
      <h2>LES TRAVAUX PRATIQUES</h2>
      <div class="equipment-grid">
        @forelse($labo->travail_pratiques as $tp)
        <div class="equipment-item">
          <div class="description">
            <h4>{{ $tp->nom }}</h4>
            <p>{{ $tp->description }}</p>
          </div>
          <div class="equipment-gallery" data-images='@json($tp->images->pluck("chemin")->toArray())'>
            @if($tp->images->isNotEmpty())
              <img src="{{ asset('storage/' . $tp->images->first()->chemin) }}" alt="{{ $tp->nom }}">
            @else
              <img src="https://images.unsplash.com/photo-1582719508461-905c673771fd" alt="{{ $tp->nom }}">
            @endif
          </div>
        </div>
        @empty
        <div class="equipment-item">
          <div class="description">
            <h4>Aucun travail pratique disponible</h4>
            <p>Les travaux pratiques seront bientôt disponibles.</p>
          </div>
        </div>
        @endforelse
      </div>
    </section>

    <section id="equipe" class="container">
      <h2>Chef laboratoire</h2>
      <div class="team-lead">
        @if($labo->chef && $labo->chef->photo)
          <img src="{{ asset('storage/' . $labo->chef->photo) }}" alt="Chef du laboratoire">
        @else
          <img src="https://images.unsplash.com/photo-1559839734-2b71ea197ec2" alt="Chef du laboratoire">
        @endif
        <div class="team-info">
          @if($labo->chef)
            <h3>{{ $labo->chef->nom }}</h3>
            <h4>Chef du Laboratoire {{ $labo->nom }}</h4>
            <p>{{ $labo->chef->bio ?? 'Responsable du laboratoire avec une expertise reconnue dans le domaine.' }}</p>
          @else
            <h3>Chef à désigner</h3>
            <h4>Poste vacant</h4>
            <p>Le laboratoire recherche actuellement un chef qualifié.</p>
          @endif
        </div>
      </div>
    </section>
  </main>

  <div class="modal" id="imageModal">
    <div class="modal-content">
      <button class="close">&times;</button>
      <div class="slideshow">
        <!-- Slides will be inserted here by JavaScript -->
      </div>
      <button class="prev">&lt;</button>
      <button class="next">&gt;</button>
    </div>
  </div>

  <footer>
    <div class="footer-content container">
      <div class="footer-section">
        <h3>Contact</h3>
        <p><i class="fas fa-envelope"></i> <EMAIL></p>
        <p><i class="fas fa-phone"></i> +33 1 23 45 67 89</p>
        <p><i class="fas fa-map-marker-alt"></i> 123 Rue de la Science, Paris</p>
      </div>
      <div class="footer-section">
        <h3>Suivez-nous</h3>
        <div class="social-links">
          <a href="https://twitter.com"><i class="fab fa-twitter"></i></a>
          <a href="https://linkedin.com"><i class="fab fa-linkedin"></i></a>
          <a href="https://facebook.com"><i class="fab fa-facebook"></i></a>
        </div>
      </div>
    </div>
    <div class="footer-bottom">
      <p>&copy; 2024 LabScience. Tous droits réservés.</p>
    </div>
  </footer>

  <script>
    // Scroll reveal animation
    function reveal() {
      const reveals = document.querySelectorAll("section");
      
      reveals.forEach(reveal => {
        const windowHeight = window.innerHeight;
        const elementTop = reveal.getBoundingClientRect().top;
        const elementVisible = 150;
        
        if (elementTop < windowHeight - elementVisible) {
          reveal.classList.add("active");
        }
      });
    }

    window.addEventListener("scroll", reveal);
    reveal();

    // Image Gallery functionality
    document.querySelectorAll('.equipment-gallery').forEach(gallery => {
      gallery.addEventListener('click', function() {
        const images = JSON.parse(this.dataset.images);
        const modal = document.getElementById('imageModal');
        const slideshow = modal.querySelector('.slideshow');
        
        slideshow.innerHTML = '';
        
        images.forEach((src, index) => {
          const slide = document.createElement('div');
          slide.className = `slide ${index === 0 ? 'active' : ''}`;
          slide.innerHTML = `<img src="/storage/${src}" alt="Equipment image ${index + 1}">`;
          slideshow.appendChild(slide);
        });
        
        modal.style.display = 'block';
      });
    });

    // Modal controls
    const modal = document.getElementById('imageModal');
    const closeBtn = modal.querySelector('.close');
    const prevBtn = modal.querySelector('.prev');
    const nextBtn = modal.querySelector('.next');

    closeBtn.onclick = () => modal.style.display = 'none';

    let currentSlide = 0;

    function showSlide(n) {
      const slides = modal.querySelectorAll('.slide');
      
      slides.forEach(slide => slide.classList.remove('active'));
      
      currentSlide = (n + slides.length) % slides.length;
      slides[currentSlide].classList.add('active');
    }

    prevBtn.onclick = () => showSlide(currentSlide - 1);
    nextBtn.onclick = () => showSlide(currentSlide + 1);

    window.onclick = (e) => {
      if (e.target === modal) modal.style.display = 'none';
    }
  </script>
</body>
</html>