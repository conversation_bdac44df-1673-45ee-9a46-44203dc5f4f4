<?php

/*use Illuminate\Support\Facades\Route;

use App\Http\Controllers\Admin\LaboratoireController;

use App\Http\Controllers\Admin\ChefController;

use App\Http\Controllers\LaboController;



/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/
  
//Route::resource('Chefs', ChefController::class);

/*Route::get('/', function () {
    return view('welcome');
});*/
/*Route::get('/laboratoires/{labo}', [LaboController::class, 'show'])->name('laboratoires.show');

Route::get('/', [LaboController::class,'index'])->name('index');



Route::prefix('admin')->name('admin.')->group( function (){
    route::resource('laboratoire' , \App\Http\Controllers\Admin\LaboratoireController::class) ->except(['show']) ;
    route::resource('chef' , \App\Http\Controllers\Admin\ChefController::class) ->except(['show']) ;
    route::resource('travail_pratiques' , \App\Http\Controllers\Admin\travail_PratiqueController::class) ->except(['show']) ;
    route::resource('galleries' , \App\Http\Controllers\Admin\GalerieController::class) ->except(['show']) ;
});*/



use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\LaboratoireController;
use App\Http\Controllers\Admin\ChefController;
use App\Http\Controllers\LaboController;

// Route de test
Route::get('/test', function () {
    return '<h1>Test Laravel - Application fonctionne !</h1><p>Nombre de laboratoires: ' . \App\Models\Laboratoire::count() . '</p>';
});

// Route de debug
Route::get('/debug', function () {
    $labos = \App\Models\Laboratoire::with('chef', 'travail_pratiques.images')->get();
    return response()->json([
        'laboratoires_count' => $labos->count(),
        'laboratoires' => $labos->toArray()
    ]);
});

// Route de test pour l'accueil
Route::get('/test-accueil', function () {
    $labos = \App\Models\Laboratoire::all()->map(function ($labo) {
        return [
            'id' => $labo->id,
            'nom' => $labo->nom,
            'description' => \Illuminate\Support\Str::limit($labo->description, 100, '...'),
            'thumbnail' => $labo->thumbnail,
        ];
    });

    return view('test-accueil', [
        'labos' => $labos,
    ]);
});

// Route d'accueil
Route::get('/', [LaboController::class, 'index'])->name('index');

// Routes pour les laboratoires
Route::get('/laboratoires', [LaboController::class, 'index'])->name('laboratoires.index');
Route::get('/laboratoires/{labo}', [LaboController::class, 'show'])->name('laboratoires.show');


// Routes pour l'administration
Route::prefix('admin')->name('admin.')->group(function () {
    Route::resource('laboratoire', \App\Http\Controllers\Admin\LaboratoireController::class)->except(['show']);
    Route::resource('chef', \App\Http\Controllers\Admin\ChefController::class)->except(['show']);
    Route::resource('travail_pratiques', \App\Http\Controllers\Admin\travail_PratiqueController::class)->except(['show']);
    Route::resource('galleries', \App\Http\Controllers\Admin\GalerieController::class)->except(['show']);
});